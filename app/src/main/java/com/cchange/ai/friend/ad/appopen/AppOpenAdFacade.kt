package com.cchange.ai.friend.ad.appopen

import android.app.Activity
import com.cchange.ai.friend.biz.remoteconf.RealRemoteConf
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.merge

class AppOpenAdFacade(
  private val admobAppOpenAdManager: AdmobAppOpenAdManager,
  private val tradPlusSplashAdManager: TradPlusSplashAdManager,
  private val remoteConfig: RealRemoteConf,
) {

  fun tryToLoadAd(activity: Activity) {
    when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusSplashAdManager.tryToLoadAd(activity)
      false -> admobAppOpenAdManager.tryToLoadAd(activity)
    }
  }

  suspend fun tryToShowAd(
    activity: Activity,
    immediate: Boolean = false
  ) {
    when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusSplashAdManager.tryToShowAd(activity, immediate)
      false -> admobAppOpenAdManager.tryToShowAd(activity, immediate)
    }
  }

  val adLoadingStateEventFlow: EventFlow<AppOpenAdLoadingStateEvent>
    get() = when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusSplashAdManager.adLoadingStateEventFlow
      false -> admobAppOpenAdManager.adLoadingStateEventFlow
    }

  val adShowStateEventFlow: EventFlow<AppOpenAdShowStateEvent>
    get() = when (remoteConfig.useLegacyAdConfig) {
      true -> tradPlusSplashAdManager.adShowStateEventFlow
      false -> admobAppOpenAdManager.adShowStateEventFlow
    }

  val mergedAdLoadingStateEventFlow: Flow<AppOpenAdLoadingStateEvent>
    get() = merge(
      admobAppOpenAdManager.adLoadingStateEventFlow,
      tradPlusSplashAdManager.adLoadingStateEventFlow
    )

  val mergedAdShowStateEventFlow: Flow<AppOpenAdShowStateEvent>
    get() = merge(
      admobAppOpenAdManager.adShowStateEventFlow,
      tradPlusSplashAdManager.adShowStateEventFlow
    )
}