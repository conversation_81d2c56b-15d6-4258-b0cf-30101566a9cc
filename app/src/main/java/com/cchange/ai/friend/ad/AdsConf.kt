@file:Suppress("PropertyName")

package com.cchange.ai.friend.ad

import kotlinx.serialization.EncodeDefault
import kotlinx.serialization.Serializable

@Serializable
data class AdsConf(
  @EncodeDefault val abtest_id: String = "",
  @EncodeDefault val fads_active_cycle_max_clicks: Int = 720,
  @EncodeDefault val fads_active_cycle_max_shows: Int = 99,
  @EncodeDefault val fads_active_cycle_minutes: Int = 99,
  @EncodeDefault val app_open_ad_conf: AppOpenAdConf,
  @EncodeDefault val banner_ad_conf: BannerAdConf,
  @EncodeDefault val interstitial_ad_conf: InterstitialAdConf,
  @EncodeDefault val rewarded_ad_conf: RewardedAdConf
) {
  companion object {
    val Default = AdsConf(
      app_open_ad_conf = AppOpenAdConf(
        ad_id = AdKitConf.APPLOVIN_MAX_APP_OPEN_AD,
        ad_show_interval_seconds = 0,
        ad_instant_load_timeout_seconds = 8
      ),
      banner_ad_conf = BannerAdConf(
        ad_id = AdKitConf.APPLOVIN_MAX_BANNER_AD
      ),
      interstitial_ad_conf = InterstitialAdConf(
        ad_id = AdKitConf.APPLOVIN_MAX_INTERSTITIAL_AD,
        ad_show_interval_seconds = 90,
        ad_instant_load_timeout_seconds = 5
      ),
      rewarded_ad_conf = RewardedAdConf(
        ad_id = AdKitConf.APPLOVIN_MAX_REWARDED_AD,
        ad_show_interval_seconds = 0,
        ad_instant_load_timeout_seconds = 5
      )
    )
  }
}

@Serializable
data class AdsConfA(
  @EncodeDefault val fads_active_cycle_max_clicks: Int = 720,
  @EncodeDefault val fads_active_cycle_max_shows: Int = 99,
  @EncodeDefault val fads_active_cycle_minutes: Int = 99,
  @EncodeDefault val app_open_ad_conf: AppOpenAdConf = AppOpenAdConf.DefaultA,
  @EncodeDefault val banner_ad_conf: BannerAdConf = BannerAdConf.DefaultA,
  @EncodeDefault val interstitial_ad_conf: InterstitialAdConf = InterstitialAdConf.DefaultA,
  @EncodeDefault val rewarded_ad_conf: RewardedAdConf = RewardedAdConf.DefaultA
)

@Serializable
data class AdsConfTp(
  @EncodeDefault val fads_active_cycle_max_clicks: Int = 720,
  @EncodeDefault val fads_active_cycle_max_shows: Int = 99,
  @EncodeDefault val fads_active_cycle_minutes: Int = 99,
  @EncodeDefault val app_ad_id: String = AdKitConf.TRAD_PLUS_APP_ID,
  @EncodeDefault val app_open_ad_conf: AppOpenAdConf = AppOpenAdConf.DefaultTp,
  @EncodeDefault val banner_ad_conf: BannerAdConf = BannerAdConf.DefaultTp,
  @EncodeDefault val interstitial_ad_conf: InterstitialAdConf = InterstitialAdConf.DefaultTp,
  @EncodeDefault val rewarded_ad_conf: RewardedAdConf = RewardedAdConf.DefaultTp
)

@Serializable
data class AppOpenAdConf(
  @EncodeDefault val ad_id: String = "",
  @EncodeDefault val ad_instant_load_timeout_seconds: Int = 0,
  @EncodeDefault val ad_show_interval_seconds: Int = 8
) {
  companion object {
    val DefaultA = AppOpenAdConf(
      ad_id = AdKitConf.ADMOB_AD_UNIT_ID_FOR_APP_OPEN,
    )

    val DefaultTp = AppOpenAdConf(
      ad_id = AdKitConf.TRAD_PLUS_APP_OPEN_AD_UNIT_ID,
    )
  }
}

@Serializable
data class BannerAdConf(
  @EncodeDefault val ad_id: String = ""
) {
  companion object {
    val DefaultA = BannerAdConf(
      ad_id = AdKitConf.ADMOB_AD_UNIT_ID_FOR_BANNER
    )

    val DefaultTp = BannerAdConf(
      ad_id = AdKitConf.TRAD_PLUS_BANNER_AD_UNIT_ID
    )
  }
}

@Serializable
data class InterstitialAdConf(
  @EncodeDefault val ad_id: String = "",
  @EncodeDefault val ad_instant_load_timeout_seconds: Int = 90,
  @EncodeDefault val ad_show_interval_seconds: Int = 5
) {
  companion object {
    val DefaultA = InterstitialAdConf(
      ad_id = AdKitConf.ADMOB_AD_UNIT_ID_FOR_INTERSTITIAL,
    )

    val DefaultTp = InterstitialAdConf(
      ad_id = AdKitConf.TRAD_PLUS_INTERSTITIAL_AD_UNIT_ID,
    )
  }
}

@Serializable
data class RewardedAdConf(
  @EncodeDefault val ad_id: String = "",
  @EncodeDefault val ad_instant_load_timeout_seconds: Int = 0,
  @EncodeDefault val ad_show_interval_seconds: Int = 8
) {
  companion object {
    val DefaultA = RewardedAdConf(
      ad_id = AdKitConf.ADMOB_AD_UNIT_ID_FOR_REWARDED,
    )

    val DefaultTp = RewardedAdConf(
      ad_id = AdKitConf.TRAD_PLUS_REWARD_AD_UNIT_ID,
    )
  }
}
