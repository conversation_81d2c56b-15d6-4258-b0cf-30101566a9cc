package com.cchange.ai.friend.ad.appopen

import android.app.Activity
import android.content.Context
import com.cchange.ai.friend.ad.AdKitConf
import com.cchange.ai.friend.ad.AppOpenAdConf
import com.cchange.ai.friend.ad.AppOpenSplashController
import com.cchange.ai.friend.ad.FullScreenAdLimitHelper
import com.cchange.ai.friend.biz.analysis.AnalysisLogger
import com.cchange.ai.friend.biz.analysis.logEventRecord
import com.cchange.ai.friend.biz.remoteconf.RealRemoteConf
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.datetime.nowInstant
import com.cchange.ai.friend.core.flow.EventFlow
import com.cchange.ai.friend.core.flow.send
import com.cchange.ai.friend.core.log.debugLog
import com.google.android.gms.ads.AdError
import com.google.android.gms.ads.AdRequest
import com.google.android.gms.ads.FullScreenContentCallback
import com.google.android.gms.ads.LoadAdError
import com.google.android.gms.ads.OnPaidEventListener
import com.google.android.gms.ads.appopen.AppOpenAd
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import kotlin.time.Duration.Companion.seconds

private const val AD_CACHE_PERIOD_SECONDS = 55 * 60L // 55 minutes

private const val TAG = "AdmobAppOpenAdManager"

class AdmobAppOpenAdManager(
  private val splashController: AppOpenSplashController,
  private val remoteConfig: RealRemoteConf,
  private val appCoroutineScope: AppCoroutineScope,
) {
  private val fullscreenAdManager: FullScreenAdLimitHelper = FullScreenAdLimitHelper

  private var appOpenAd: AppOpenAd? = null
  private var isLoadingAd = false
  private var latestLoadAdSuccessInstant: Instant = Instant.fromEpochSeconds(0)

  private suspend fun adKey()
    = remoteConfig.adsConfAdmob().app_open_ad_conf.ad_id.ifEmpty { AdKitConf.ADMOB_AD_UNIT_ID_FOR_APP_OPEN }

  private suspend fun appOpenAdConfig(): AppOpenAdConf =
    remoteConfig.adsConfAdmob().app_open_ad_conf

  val adLoadingStateEventFlow = EventFlow<AppOpenAdLoadingStateEvent>()
  val adShowStateEventFlow = EventFlow<AppOpenAdShowStateEvent>()

  private var sendLoadingTimeOutJob: Job? = null

  fun tryToLoadAd(activity: Activity) {
    debugLog(tag = TAG) { "tryToLoadAd" }

    if (isLoadingAd) return

    if (isAdAvailable()) {
      debugLog(tag = TAG) { "hasAdAvailable" }

//      adLoadingStateEventFlow.send(AdLoadingStateEvent.SkipToLoad)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.SkipToLoad)" }
    } else {
      debugLog(tag = TAG) { "noAdAvailable" }

      loadAd(activity)
    }
  }

  private fun loadAd(context: Context) {
    sendLoadingTimeOutJob?.cancel()
    sendLoadingTimeOutJob = null
    sendLoadingTimeOutJob = appCoroutineScope.launch(Dispatchers.Default) {
      delay(appOpenAdConfig().ad_instant_load_timeout_seconds.seconds)
      debugLog(tag = TAG) { "send(AdLoadingStateEvent.TimeOut)" }
      adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.TimeOut)
    }

    if (isLoadingAd) {
      debugLog(tag = TAG) { "do not loadAd, cuz isLoadingAd" }
      return
    }

    debugLog(tag = TAG) { "loadAd" }

    isLoadingAd = true

    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      AppOpenAd.load(
        context,
        adKey(),
        AdRequest.Builder().build(),
        object : AppOpenAd.AppOpenAdLoadCallback() {
          override fun onAdLoaded(ad: AppOpenAd) {
            debugLog(tag = TAG) { "onAdLoaded" }

            ad.onPaidEventListener = OnPaidEventListener { adValue ->
              val adSourceName = ad.responseInfo.loadedAdapterResponseInfo?.adSourceName
              val adFormat = "app_open"
              val adUnitId = ad.adUnitId

              AnalysisLogger.tryToRecordTotalAdsRevenue001(adValue, adSourceName)
              AnalysisLogger.recordAdImpressionRevenue(
                adValue,
                adSourceName,
                adFormat,
                ""
              )
              AnalysisLogger.recordAdImpression(
                adValue,
                adSourceName,
                adFormat,
                adUnitId
              )

            }

            appOpenAd = ad
            isLoadingAd = false
            latestLoadAdSuccessInstant = nowInstant()

            sendLoadingTimeOutJob?.cancel()
            sendLoadingTimeOutJob = null
            adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.Loaded)
            debugLog(tag = TAG) { "send(AdLoadingStateEvent.Loaded)" }

            logEventRecord("ad_app_open_load_success")
          }

          override fun onAdFailedToLoad(loadAdError: LoadAdError) {
            debugLog(tag = TAG) { "onAdFailedToLoad" }

            isLoadingAd = false

            sendLoadingTimeOutJob?.cancel()
            sendLoadingTimeOutJob = null
            adLoadingStateEventFlow.send(AppOpenAdLoadingStateEvent.FailedToLoad)
          }
        }
      )
    }

    logEventRecord("ad_app_open_load")
  }

  private fun isAdAvailable(): Boolean {
    return appOpenAd != null && checkAdIsValidAtCachePeriod()
  }

  private fun checkAdIsValidAtCachePeriod(adCachePeriodSeconds: Long = AD_CACHE_PERIOD_SECONDS): Boolean {
    val secondsDifference: Long = nowInstant().epochSeconds - latestLoadAdSuccessInstant.epochSeconds
    return secondsDifference < adCachePeriodSeconds
  }

  private fun showAd(activity: Activity) {
    debugLog(tag = TAG) { "showAd" }

    logEventRecord("ad_app_open_show")

    appOpenAd?.fullScreenContentCallback = object : FullScreenContentCallback() {
      override fun onAdDismissedFullScreenContent() {
        appOpenAd = null
        loadAd(activity)
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.Finish)
      }

      override fun onAdFailedToShowFullScreenContent(adError: AdError) {
        debugLog(tag = TAG) { "onAdFailedToShowFullScreenContent" }
        appOpenAd = null
        loadAd(activity)
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.FailedToShow)
      }

      override fun onAdShowedFullScreenContent() {
        FullScreenAdLimitHelper.displayOnce()
        adShowStateEventFlow.send(AppOpenAdShowStateEvent.Showing)
        debugLog(tag = TAG) { "onAdShowedFullScreenContent" }
      }

      override fun onAdClicked() {
        splashController.skipSplash(true)
        logEventRecord("ad_app_open_click")
      }

      override fun onAdImpression() {
        logEventRecord("ad_app_open_impress")
      }
    }

    appOpenAd?.show(activity)
  }

  suspend fun tryToShowAd(
    activity: Activity,
    immediate: Boolean = false
  ) = withContext(Dispatchers.Main.immediate) {
    debugLog(tag = TAG) { "tryToShowAd" }

    if (fullscreenAdManager.isLimit()) {
      debugLog(tag = TAG) { "isAdShowTimeInShowInterval" }
      adShowStateEventFlow.send(AppOpenAdShowStateEvent.SkipToShow)
    } else { // over the show interval, need to show ad
      debugLog(tag = TAG) { "over the show interval, need to show ad" }
      if (isAdAvailable()) { // cache available
        debugLog(tag = TAG) { "cache available" }
        if (!immediate) {
          delay(1_000)
        }
        showAd(activity)
      } else { // cache not available
        debugLog(tag = TAG) { "cache not available" }
        loadAd(activity)
      }
    }
  }
}