package com.cchange.ai.friend.biz.analysis

import android.content.Context
import android.os.Bundle
import com.applovin.mediation.MaxAd
import com.cchange.ai.friend.BuildConfig
import com.cchange.ai.friend.biz.bi.BiReporter
import com.cchange.ai.friend.biz.bi.reportAdOnPaid
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.log.debugLog
import com.google.android.gms.ads.AdValue
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.analytics.analytics
import com.google.firebase.Firebase
import com.tradplus.ads.base.bean.TPAdInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object AnalysisLogger : KoinComponent {

  private const val TAG = "AnalyticsLogEvent"

  private val context: Context by inject()
  private val appCoroutineScope: AppCoroutineScope by inject()

  //  private val tenjinHelper: TenjinHelper by inject()
  private val analysisPref: AnalysisPref by inject()
  private val biReporter: BiReporter by inject()
  private val appsFlyerHelper: AppsFlyerHelper by inject()


  fun record(eventName: String, args: Bundle? = Bundle()) {
    if (BuildConfig.DEBUG) return

    appCoroutineScope.launch(Dispatchers.Default) {
      if (args == null) {
        debugLog("$TAG eventName:{$eventName}")
      }

      Firebase.analytics.logEvent(eventName, args)
    }
  }

  fun revenueRecord(eventName: String, args: Bundle? = Bundle()) {
    if (BuildConfig.DEBUG) return

    appCoroutineScope.launch(Dispatchers.Default) {
      if (args == null) {
        debugLog("$TAG eventName:{$eventName}")
      }

      Firebase.analytics.logEvent(eventName, args)
    }
  }

  fun tryToRecordTotalAdsRevenue001(
    adFormat: String,
    adValue: Double,
    adNetwork: String,
    adUnitId: String,
  ) {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      val total = analysisPref.totalAdsRevenue()

      val newTotal = total + adValue

      if (newTotal >= 0.01) {
        logEventAdRevenueRecord("Total_Ads_Revenue_001") {
          putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", adNetwork)
          putString("adFormat", adFormat)
        }

        analysisPref.setTotalAdsRevenue(0.0)
      } else {
        analysisPref.setTotalAdsRevenue(newTotal)
      }
    }
  }

  fun tryToRecordTotalAdsRevenue001(
    adValue: AdValue?,
    adSourceName: String?,
  ) {
    appCoroutineScope.launch(Dispatchers.Main.immediate) {
      val valueMicros = adValue?.valueMicros ?: return@launch

      val revenue = valueMicros / (1_000_000).toDouble()

      val total = analysisPref.totalAdsRevenue()
      val newTotal = total + revenue

      if (newTotal >= 0.01) {
        logEventAdRevenueRecord("Total_Ads_Revenue_001") {
          putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", adSourceName ?: "")
          putString("precisionType", adValue.precisionType.toString())
        }

        analysisPref.setTotalAdsRevenue(0.0)
      } else {
        analysisPref.setTotalAdsRevenue(newTotal)
      }
    }
  }

  fun tryToRecordTotalAdsRevenue001(
    tpAdInfo: TPAdInfo,
  ) {
    appCoroutineScope.launch(Dispatchers.Default) {
      val ecpm = tpAdInfo.ecpm?.toDouble() ?: return@launch

      val revenue = ecpm / 1_000

      val total = analysisPref.totalAdsRevenue()
      val newTotal = total + revenue

      if (newTotal >= 0.01) {
        logEventAdRevenueRecord("Total_Ads_Revenue_001") {
          putDouble(FirebaseAnalytics.Param.VALUE, newTotal)
          putString(FirebaseAnalytics.Param.CURRENCY, "USD")
          putString("adNetwork", tpAdInfo.adSourceName)
          putString("precisionType", tpAdInfo.ecpmPrecision)
        }

        analysisPref.setTotalAdsRevenue(0.0)
      } else {
        analysisPref.setTotalAdsRevenue(newTotal)
      }
    }
  }

  fun recordAdImpressionRevenue(
    adValue: AdValue?,
    adSourceName: String?,
    adFormat: String,
    adPlacement: String,
  ) {
    val valueMicros = adValue?.valueMicros ?: return

    val revenue = valueMicros / (1_000_000).toDouble()

    logEventAdRevenueRecord("Ad_Impression_Revenue") {
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
      putString("adNetwork", adSourceName ?: "")
      putString("precisionType", adValue.precisionType.toString())
    }
  }

  fun recordAdImpressionRevenue(
    tpAdInfo: TPAdInfo?,
    adFormat: String,
  ) {
    val ecpm = tpAdInfo?.ecpm?.toDouble() ?: return

    val revenue = ecpm / 1_000

    logEventAdRevenueRecord("Ad_Impression_Revenue") {
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
      putString("adNetwork", tpAdInfo.adSourceName)
      putString("adFormat", adFormat)
    }
  }


  fun recordAdImpression(
    adValue: AdValue?,
    adSourceName: String?,
    adFormat: String,
    adUnit: String,
  ) {
    val valueMicros = adValue?.valueMicros ?: return

    val revenue = valueMicros / (1_000_000).toDouble()

    logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
      putString(FirebaseAnalytics.Param.AD_SOURCE, adSourceName)
      putString(FirebaseAnalytics.Param.AD_FORMAT, adFormat)
      putString(FirebaseAnalytics.Param.AD_UNIT_NAME, adUnit)
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
    }

    biLogEventAdOnPaid(
      value = revenue.toFloat(),
      currency = "USD",
      precisionType = adValue.precisionType.toString(),
      adNetwork = adSourceName ?: "",
      adType = adFormat,
    )
  }

  fun recordAdImpression(
    tpAdInfo: TPAdInfo?,
    adFormat: String,
  ) {
    val ecpm = tpAdInfo?.ecpm?.toDouble() ?: return

    val revenue = ecpm / 1_000

    logEventAdRevenueRecord(FirebaseAnalytics.Event.AD_IMPRESSION) {
      putString(FirebaseAnalytics.Param.AD_PLATFORM, "TradPlus")
      putString(FirebaseAnalytics.Param.AD_SOURCE, tpAdInfo.adSourceName)
      putString(FirebaseAnalytics.Param.AD_FORMAT, adFormat)
      putString(FirebaseAnalytics.Param.AD_UNIT_NAME, tpAdInfo.tpAdUnitId)
      putDouble(FirebaseAnalytics.Param.VALUE, revenue)
      putString(FirebaseAnalytics.Param.CURRENCY, "USD")
    }

    biLogEventAdOnPaid(
      value = revenue.toFloat(),
      currency = "USD",
      precisionType = tpAdInfo.ecpmPrecision,
      adNetwork = tpAdInfo.adSourceName ?: "",
      adType = adFormat,
    )
  }

  fun logAdViewEvent(
    context: Context,
    maxAd: MaxAd,
  ) {
    appsFlyerHelper.logAdViewEvent(
      context = context,
      adType = maxAd.format.label ?: "",
      placementId = maxAd.placement ?: "",
      revenue = maxAd.revenue.toString(),
      currency = "USD",
      networkName = maxAd.networkName ?: ""
    )

    appsFlyerHelper.logAdRevenue(maxAd)
  }

  fun biLogEventAdOnPaid(
    value: Float,
    currency: String,
    precisionType: String,
    adNetwork: String,
    adType: String,
    adPlacement: String = "",
  ) {
    appCoroutineScope.launch(Dispatchers.Default) {
      biReporter.reportAdOnPaid(value, currency, precisionType, adNetwork, adType, adPlacement)
    }
  }
}

@Suppress("NOTHING_TO_INLINE")
inline fun logEventRecord(eventName: String) {
  AnalysisLogger.record(eventName)
}

inline fun logEventRecord(eventName: String, argsBlock: Bundle.() -> Unit) {
  AnalysisLogger.record(eventName, Bundle().apply(argsBlock))
}

inline fun logEventAdRevenueRecord(eventName: String, argsBlock: Bundle.() -> Unit) {
  AnalysisLogger.revenueRecord(eventName, Bundle().apply(argsBlock))
}