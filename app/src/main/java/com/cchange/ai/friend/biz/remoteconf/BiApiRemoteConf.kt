package com.cchange.ai.friend.biz.remoteconf

import android.content.Context
import com.cchange.ai.friend.ad.AdsConf
import com.cchange.ai.friend.ad.AdsConfA
import com.cchange.ai.friend.ad.AdsConfTp
import com.cchange.ai.friend.ad.AdsProvider
import com.cchange.ai.friend.biz.bi.BiApi
import com.cchange.ai.friend.biz.bi.BiApiConfigRequestBody
import com.cchange.ai.friend.biz.bi.BiPrefs
import com.cchange.ai.friend.biz.bi.BiReporter
import com.cchange.ai.friend.biz.noti.repeat.RemoteConfigMessageRepeatNotification
import com.cchange.ai.friend.biz.noti.repeat.RepeatNotiGroup
import com.cchange.ai.friend.biz.noti.repeat.RepeatNotiPushStrategy
import com.cchange.ai.friend.biz.noti.repeat.UniRepeatNoti
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.crypt.requestBodyEncrypt
import com.cchange.ai.friend.core.log.debugLog
import com.cchange.ai.friend.core.serialization.json.toJson
import com.cchange.ai.friend.core.serialization.json.toObj
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.time.DurationUnit
import kotlin.time.toDuration

class BiApiRemoteConf(
  private val context: Context,
  private val biApi: BiApi,
  private val appCoroutineScope: AppCoroutineScope,
  private val biReporter: BiReporter,
  private val biPrefs: BiPrefs,
) {
  companion object Companion {
    const val TAG = "BiApiRemoteConfig"
  }

  fun init(
    onUnstableUpdateConfig: (() -> Unit)? = null,
  ) {
    debugLog(tag = TAG) { "init()" }
    appCoroutineScope.launch(Dispatchers.Default) {
      if (biPrefs.remoteConf().isNullOrEmpty()) {
        repeat(50) { currentTimes ->
          fetchConfig()
          onUnstableUpdateConfig?.invoke()

          when (currentTimes) {
            in 0..12 -> {
              delay(10.toDuration(DurationUnit.SECONDS))
            }

            in 13..17 -> {
              val input = currentTimes

              // 计算系数：(n-12)^2 - n + 13 - [if n=16 then 1 else 0]
              val adjustment = if (input == 16) 1 else 0
              val coefficient = (input - 12) * (input - 12) - input + 13 - adjustment
              val result = coefficient * 5

              delay(result.toDuration(DurationUnit.MINUTES))
            }

            else -> {
              delay(60.toDuration(DurationUnit.MINUTES))
            }
          }
        }
      } else {
        while (true) {
          fetchConfig()
          delay(60.toDuration(DurationUnit.MINUTES))
        }
      }
    }
  }

  private suspend fun fetchConfig() {
    debugLog(tag = TAG) { "fetchConfig()" }

    var header = biReporter.header()

    if (header == null) {
      run {
        repeat(7) {
          delay(100L * (it + 1))

          debugLog(tag = TAG) { "repeat getHeader $it" }
          header = biReporter.header()
          if (header != null) {
            debugLog(tag = TAG) { "repeat getHeader non null" }
            return@run
          }
        }
      }
    }

    if (header != null) {
      val configMap = try {
        biApi.config(
          headerMap = header,
          body = BiApiConfigRequestBody().toJson().requestBodyEncrypt()
        )
      } catch (e: Exception) {
        e.printStackTrace()
        null
      }
      debugLog(tag = TAG) { "get configMap: ${configMap.isNullOrEmpty().not()}" }

      if (configMap.isNullOrEmpty().not()) {

        biPrefs.storeRemoteConf(configMap)

        appCoroutineScope.launch(Dispatchers.Default) {
//     todo     configureTradPlusSdk(context)
        }
      }
    }
  }

  suspend fun realAbTestId(): String? {
    val adsConf = adsConf() ?: return null

    return buildString {
      append(adsConf.abtest_id)
    }
  }

  suspend fun adsConf(): AdsConf? {
    val key = RemoteConfKey.ADS_CONF

    return biPrefs.remoteConf()?.get(key)?.toObj()
  }

  suspend fun repeatNotiPushStrategy(
    repeatNotification: RemoteConfigMessageRepeatNotification
  ): RepeatNotiPushStrategy? {
    val key = when (repeatNotification) {
      is UniRepeatNoti -> RemoteConfKey.UNI_REPEAT_NOTI_PUSH_STRATEGY
    }

    return biPrefs.remoteConf()?.get(key)?.toObj()
  }

  suspend fun repeatNotiGroup(
    repeatNotification: RemoteConfigMessageRepeatNotification
  ): RepeatNotiGroup? {
    val key = when (repeatNotification) {
      is UniRepeatNoti -> RemoteConfKey.UNI_NOTI_GROUP
    }

    return biPrefs.remoteConf()?.get(key)?.toObj()
  }

  suspend fun adsProvider(): AdsProvider? {
    val key = RemoteConfKey.ADS_PROVIDER

    return biPrefs.remoteConf()?.get(key)?.toObj()
  }

  suspend fun adsConfAdmob(): AdsConfA? {
    val key = RemoteConfKey.ADS_CONF_ADMOB

    return biPrefs.remoteConf()?.get(key)?.toObj()
  }

  suspend fun adsConfTp(): AdsConfTp? {
    val key = RemoteConfKey.ADS_CONF_TP

    return biPrefs.remoteConf()?.get(key)?.toObj()
  }
}