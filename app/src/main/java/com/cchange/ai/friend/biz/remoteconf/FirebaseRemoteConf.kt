package com.cchange.ai.friend.biz.remoteconf

import com.cchange.ai.friend.ad.AdsConf
import com.cchange.ai.friend.ad.AdsConfA
import com.cchange.ai.friend.ad.AdsConfTp
import com.cchange.ai.friend.ad.AdsProvider
import com.cchange.ai.friend.biz.noti.repeat.RemoteConfigMessageRepeatNotification
import com.cchange.ai.friend.biz.noti.repeat.RepeatNotiGroup
import com.cchange.ai.friend.biz.noti.repeat.RepeatNotiPushStrategy
import com.cchange.ai.friend.biz.noti.repeat.UniRepeatNoti
import com.cchange.ai.friend.core.coroutine.AppCoroutineScope
import com.cchange.ai.friend.core.crypt.decryptCBC
import com.cchange.ai.friend.core.datetime.nowInstant
import com.cchange.ai.friend.core.serialization.json.toObj
import com.google.firebase.Firebase
import com.google.firebase.remoteconfig.FirebaseRemoteConfigSettings
import com.google.firebase.remoteconfig.get
import com.google.firebase.remoteconfig.remoteConfig
import com.russhwolf.settings.coroutines.SuspendSettings
import kotlinx.coroutines.launch
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject

object FirebaseRemoteConf : KoinComponent {

  object RemoteConfPref : KoinComponent {

    private const val TAG = "RemoteConfPref"

    private val suspendSettings: SuspendSettings by inject()

    private const val LATEST_UPDATE_INSTANT_SECOND_KEY = TAG + "_latestUpdateInstantSecond"
    suspend fun latestUpdateInstantSeconds(): Long {
      return suspendSettings.getLong(LATEST_UPDATE_INSTANT_SECOND_KEY, 0L)
    }

    suspend fun setLatestUpdateInstantSeconds(instant: Instant) {
      suspendSettings.putLong(LATEST_UPDATE_INSTANT_SECOND_KEY, instant.epochSeconds)
    }
  }

  private val firebaseRemoteConfig get() = Firebase.remoteConfig

  private val appCoroutineScope: AppCoroutineScope by inject()

  fun tryToUpdateConfig(now: Instant = nowInstant()) {
    appCoroutineScope.launch {
      val latestUpdateInstantSeconds = RemoteConfPref.latestUpdateInstantSeconds()

      if (now.epochSeconds - latestUpdateInstantSeconds >= 60 * 60L) { // 1 hour
        fetch {
          appCoroutineScope.launch {
            RemoteConfPref.setLatestUpdateInstantSeconds(now)
          }
        }
      }
    }
  }

  fun fetch(onSuccess: (() -> Unit)? = null) {
    firebaseRemoteConfig.reset().addOnCompleteListener {
      firebaseRemoteConfig.apply {
        setConfigSettingsAsync(
          FirebaseRemoteConfigSettings.Builder().setMinimumFetchIntervalInSeconds(30 * 60L).build()
        )
        fetchAndActivate().addOnCompleteListener {
          if (it.isSuccessful) {
            onSuccess?.invoke()
          }
        }
      }
    }
  }

  fun realAbTestId(): String? {
    val adsConf = adsConf() ?: return null

    return buildString {
      append(adsConf.abtest_id)
    }
  }

  fun adsConf(): AdsConf? {
    val key = RemoteConfKey.ADS_CONF

    return try {
      firebaseRemoteConfig[key].asString().decryptCBC().toObj<AdsConf>()
    } catch (e: Exception) {
      e.printStackTrace()
      null
    }
  }

  fun repeatNotiPushStrategy(
    repeatNotification: RemoteConfigMessageRepeatNotification
  ): RepeatNotiPushStrategy {
    val (key, defaultStrategy) = when (repeatNotification) {
      is UniRepeatNoti -> {
        RemoteConfKey.UNI_REPEAT_NOTI_PUSH_STRATEGY to RepeatNotiPushStrategy.Default
      }
    }

    return firebaseRemoteConfig[key].asString().toObj<RepeatNotiPushStrategy>() ?: defaultStrategy
  }

  fun repeatNotiGroup(
    repeatNotification: RemoteConfigMessageRepeatNotification
  ): RepeatNotiGroup {
    val (key, defaultGroup) = when (repeatNotification) {
      is UniRepeatNoti -> {
        RemoteConfKey.UNI_NOTI_GROUP to RepeatNotiGroup.Default
      }
    }

    return firebaseRemoteConfig[key].asString().toObj<RepeatNotiGroup>() ?: defaultGroup
  }

  fun adsProvider(): AdsProvider {
    val key = RemoteConfKey.ADS_PROVIDER

    return try {
      firebaseRemoteConfig[key].asString().toObj<AdsProvider>() ?: AdsProvider.A
    } catch (e: Exception) {
      e.printStackTrace()
      AdsProvider.A
    }
  }

  fun adsConfAdmob(): AdsConfA {
    val key = RemoteConfKey.ADS_CONF_ADMOB

    return try {
      firebaseRemoteConfig[key].asString().decryptCBC().toObj<AdsConfA>() ?: AdsConfA()
    } catch (e: Exception) {
      e.printStackTrace()
      AdsConfA()
    }
  }

  fun adsConfTp(): AdsConfTp {
    val key = RemoteConfKey.ADS_CONF_TP

    return try {
      firebaseRemoteConfig[key].asString().decryptCBC().toObj<AdsConfTp>() ?: AdsConfTp()
    } catch (e: Exception) {
      e.printStackTrace()
      AdsConfTp()
    }
  }
}