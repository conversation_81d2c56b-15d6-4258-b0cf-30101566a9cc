package com.cchange.ai.friend.ad

import com.cchange.ai.friend.biz.remoteconf.RealRemoteConf
import com.russhwolf.settings.coroutines.SuspendSettings
import kotlinx.datetime.Instant
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import kotlin.time.Duration.Companion.minutes

// Interface to extract common limit configuration from different ad configs
@Suppress("PropertyName")
interface AdLimitConfig {
  val fads_active_cycle_max_clicks: Int
  val fads_active_cycle_max_shows: Int
  val fads_active_cycle_minutes: Int
}

// Extension functions to make existing classes implement the interface
fun AdsConf.asAdLimitConfig(): AdLimitConfig = object : AdLimitConfig {
  override val fads_active_cycle_max_clicks = this@asAdLimitConfig.fads_active_cycle_max_clicks
  override val fads_active_cycle_max_shows = this@asAdLimitConfig.fads_active_cycle_max_shows
  override val fads_active_cycle_minutes = this@asAdLimitConfig.fads_active_cycle_minutes
}

fun AdsConfA.asAdLimitConfig(): AdLimitConfig = object : AdLimitConfig {
  override val fads_active_cycle_max_clicks = this@asAdLimitConfig.fads_active_cycle_max_clicks
  override val fads_active_cycle_max_shows = this@asAdLimitConfig.fads_active_cycle_max_shows
  override val fads_active_cycle_minutes = this@asAdLimitConfig.fads_active_cycle_minutes
}

fun AdsConfTp.asAdLimitConfig(): AdLimitConfig = object : AdLimitConfig {
  override val fads_active_cycle_max_clicks = this@asAdLimitConfig.fads_active_cycle_max_clicks
  override val fads_active_cycle_max_shows = this@asAdLimitConfig.fads_active_cycle_max_shows
  override val fads_active_cycle_minutes = this@asAdLimitConfig.fads_active_cycle_minutes
}

@Suppress("FunctionName")
object FullScreenAdLimitHelper : KoinComponent {

  private val realRemoteConf: RealRemoteConf by inject()

  private suspend fun getAdLimitConfig(): AdLimitConfig {
    return when (realRemoteConf.adsProvider()) {
      AdsProvider.A -> realRemoteConf.adsConfAdmob().asAdLimitConfig()
      AdsProvider.TP -> realRemoteConf.adsConfTp().asAdLimitConfig()
    }
  }

  private const val TAG = "FullScreenAdLimitHelper"

  private suspend fun MAX_SHOWS() = getAdLimitConfig().fads_active_cycle_max_shows
  private suspend fun MAX_CLICKS() = getAdLimitConfig().fads_active_cycle_max_clicks
  private suspend fun MAX_ACTIVE_CYCLE_DURATION() = getAdLimitConfig().fads_active_cycle_minutes.minutes

  private val suspendSettings: SuspendSettings by inject()

  var displayTimes: Int = 0
    private set

  var clickTimes: Int = 0
    private set


  // invoke in TimeTickReceiver
  suspend fun calculateActiveCycle(
    now: Instant
  ) {
    if (now - latestActiveStartInstant() > MAX_ACTIVE_CYCLE_DURATION()) {
      entryNewCycle(now)
    }
  }

  fun displayOnce() {
    displayTimes++
  }

  fun clickOnce() {
    clickTimes++
  }

  suspend fun isLimit(): Boolean {
    return displayTimes > MAX_SHOWS() || clickTimes > MAX_CLICKS()
  }

  private suspend fun entryNewCycle(now: Instant) {
    displayTimes = 0
    clickTimes = 0
    setLatestActiveStartInstant(now)
  }

  // ---------------------------------------------------------------------------------------------
  private const val KEY_LATEST_ACTIVE_INSTANT = "${TAG}_latestActiveStartInstant"

  private suspend fun latestActiveStartInstant(): Instant {
    return suspendSettings
      .getLong(KEY_LATEST_ACTIVE_INSTANT, 0L)
      .let { Instant.fromEpochSeconds(it) }
  }

  private suspend fun setLatestActiveStartInstant(instant: Instant) {
    suspendSettings.putLong(KEY_LATEST_ACTIVE_INSTANT, instant.epochSeconds)
  }

}