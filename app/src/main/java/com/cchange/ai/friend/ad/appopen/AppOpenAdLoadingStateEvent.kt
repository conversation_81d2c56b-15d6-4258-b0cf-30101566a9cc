package com.cchange.ai.friend.ad.appopen

sealed interface AppOpenAdLoadingStateEvent {
  data object TimeOut : AppOpenAdLoadingStateEvent
  data object Loaded : AppOpenAdLoadingStateEvent
  data object FailedToLoad : AppOpenAdLoadingStateEvent
}

sealed interface AppOpenAdShowStateEvent {
  data object Finish : AppOpenAdShowStateEvent
  data object Showing : AppOpenAdShowStateEvent
  data object FailedToShow : AppOpenAdShowStateEvent
  data object SkipToShow : AppOpenAdShowStateEvent
}